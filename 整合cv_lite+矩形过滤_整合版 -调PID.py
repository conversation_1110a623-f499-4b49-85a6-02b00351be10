import time,os,urandom,sys,gc#,image#如果要显示照片，则引用image模块处理图片信息 | 导入必要的模块：时间、操作系统、串口、系统、垃圾回收
from media.display import *
from media.media import *#显示图像需要用到Media的display和media模块
from media.sensor import *
from machine import FPIOA
from machine import Pin
from machine import UART
from ybUtils.YbUart import YbUart
import cv_lite  # 导入cv_lite模块用于高级矩形检测

WIDTH = 320
HEIGHT = 240

# X轴PID参数（步进电机）
X_P_GAIN = -0.8 # 增大比例增益，提高小误差响应
X_I_GAIN = -0.1 # 增大积分增益，加快小误差累积
X_D_GAIN = -0.94 # 增大微分增益，提高响应速度
X_DEAD_ZONE = 0.670  # 减小死区，对更小误差响应
X_MAX_INTEGRAL = 800  # 适当减小积分限幅
X_MIN_STEPS = 2  # 添加最小步数阈值

# Y轴PID参数（串口舵机）
Y_P_GAIN = -0.3    # Y轴比例增益
Y_I_GAIN = -0.00   # Y轴积分增益
Y_D_GAIN = -0.1    # Y轴微分增益
Y_DEAD_ZONE = 2 # Y轴死区
Y_MAX_INTEGRAL = 500  # Y轴积分限幅

# 追踪稳定性检测参数
STABLE_THRESHOLD = 100     # 稳定阈值：误差小于此值认为稳定
STABLE_COUNT_REQUIRED = 3  # 需要连续稳定的帧数
STABLE_COMMAND = b'\xaa'    # 稳定后发送的指令（16进制：0xAA）
START_COMMAND = b'\xab'     # 开始识别指令（16进制：0xAB）
FIND_RECT_COMMAND = b'\xcc' # 开始找矩形指令（16进制：0xCC）
RECT_FOUND_COMMAND = b'\xc1' # 找到矩形指令（16进制：0xC1）
stable_command_sent = False

# 识别控制变量
recognition_enabled = False  # 识别开关，默认关闭
find_rect_mode = False      # 找矩形模式，默认关闭
rect_found_sent = False     # 是否已发送找到矩形指令
rect_found_count = 0        # 连续找到矩形的次数
RECT_FOUND_REQUIRED = 3     # 需要连续找到矩形的次数才发送0xC1

# cv_lite矩形检测参数 - 进一步优化倾斜矩形
image_shape = [HEIGHT, WIDTH]  # 图像形状：[高，宽]
canny_thresh1       = 30        # 进一步降低低阈值，捕获微弱边缘
canny_thresh2       = 90        # 进一步降低高阈值
approx_epsilon      = 0.12      # 大幅增加拟合精度，允许更不规则形状
area_min_ratio      = 0.008      # 大幅降低最小面积要求
max_angle_cos       = 0.9       # 进一步放宽角度限制，接近允许任意角度
gaussian_blur_size  = 7         # 增大模糊核，更好地连接断裂边缘

#透视补偿
rect_compensate = 10 #加到目标点

try:
    class Rectangle:
        """矩形类，兼容原有接口"""
        def __init__(self, x, y, w, h):
            self._x = x
            self._y = y
            self._w = w
            self._h = h

        def x(self):
            return self._x

        def y(self):
            return self._y

        def w(self):
            return self._w

        def h(self):
            return self._h

        def rect(self):
            return (self._x, self._y, self._w, self._h)

    uart = YbUart(baudrate=115200)

    # X轴PID控制器变量初始化
    x_error_integral = 0  # X轴积分项累积
    x_error_last = 0      # X轴上一次误差，用于计算微分项

    # Y轴PID控制器变量初始化
    y_error_integral = 0  # Y轴积分项累积
    y_error_last = 0      # Y轴上一次误差，用于计算微分项
    y_current_position = 1500  # Y轴当前位置（初始为中心位置90度）

    # 追踪稳定性检测变量
    stable_count = 0        # 连续稳定的帧数计数
    is_tracking_stable = False  # 追踪是否稳定
    stable_command_sending = False # 是否正在发送稳定指令


    fpioa = FPIOA()#初始化FPIOA对象
    #设置按键
    fpioa.set_function(61, FPIOA.GPIO61)#确定该端口的功能为GPIO
    key = Pin(61, Pin.IN, Pin.PULL_DOWN)#设置fpioa61端口的模式，创建该端口对象

    #设置UART3_TXD
    fpioa.set_function(33, FPIOA.UART3_RXD, ie=1, oe=0)
    pin33 = Pin(33,Pin.OUT)#设置fpioa32端口的模式，创建该端口的对象
    #设置UART3_RXD
    fpioa.set_function(32, FPIOA.UART3_TXD, ie=0, oe=1)
    pin32 = Pin(32,Pin.OUT)#设置fpioa32端口的模式，创建该端口的对象

    uart3 = UART(UART.UART3, 115200)



    sensor = Sensor()   #初始化传感器变量
    sensor.reset()      #传感器重置
    sensor.set_framesize(width = WIDTH, height = HEIGHT, chn = CAM_CHN_ID_0)#设置通道0的输出尺寸
    sensor.set_pixformat(Sensor.GRAYSCALE,chn = CAM_CHN_ID_0)#设置通道0的输出格式为灰度图
    Display.init(Display.ST7701, width = 640, height = 480, to_ide = True)
    MediaManager.init()#初始化媒体管理器
    sensor.run()#开启传感器
    fps = time.clock()#创建时钟对象用于计算帧率

    while True:
        fps.tick()#帧率计时器tick
        os.exitpoint()#检查是否退出程序

        # 检查串口接收到的控制指令
        if uart.any():
            received_data = uart.read()
            if START_COMMAND in received_data:
                print("收到开始指令0xAB，开始识别矩形")
                uart.send(STABLE_COMMAND)
                recognition_enabled = True
                find_rect_mode = False
                # 重置PID状态
                x_error_integral = 0
                x_error_last = 0
                y_error_integral = 0
                y_error_last = 0
                # 重置稳定状态
                stable_count = 0
                is_tracking_stable = False
                stable_command_sent = False
            elif FIND_RECT_COMMAND in received_data:
                print("收到找矩形指令0xCC，开始找矩形")
                find_rect_mode = True
                recognition_enabled = False
                rect_found_sent = False
                rect_found_count = 0  # 重置计数

        img = sensor.snapshot(chn = CAM_CHN_ID_0)

        # 只有在识别开启时才进行矩形检测
        if recognition_enabled or find_rect_mode:
            # 获取图像的numpy数组引用
            img_np = img.to_numpy_ref()

            # 调用cv_lite矩形检测函数
            rects_data = cv_lite.grayscale_find_rectangles(
                image_shape, img_np,
                canny_thresh1, canny_thresh2,
                approx_epsilon,
                area_min_ratio,
                max_angle_cos,
                gaussian_blur_size
            )

            # 直接使用检测结果，不进行筛选
            if rects_data and len(rects_data) >= 4:
                # 取第一个检测到的矩形
                x, y, w, h = rects_data[0], rects_data[1], rects_data[2], rects_data[3]
                rect = Rectangle(x, y, w, h)
                print(f"cv_lite检测到 {len(rects_data)//4} 个矩形，使用第一个")

                # 如果是找矩形模式且还未发送找到指令
                if find_rect_mode and not rect_found_sent:
                    rect_found_count += 1
                    print(f"连续找到矩形次数: {rect_found_count}/{RECT_FOUND_REQUIRED}")

                    if rect_found_count >= RECT_FOUND_REQUIRED:
                        uart.send(RECT_FOUND_COMMAND)
                        print("发送找到矩形指令0xC1")
                        rect_found_sent = True
            else:
                rect = None
                print("cv_lite未检测到矩形")

                # 如果是找矩形模式，重置计数
                if find_rect_mode:
                    rect_found_count = 0

            if rect:
                # 绘制识别到的矩形和中心点
                img.draw_rectangle(rect.rect(), color = (255, 0, 0))
                local_x, local_y = (rect.x() +int(rect.w() / 2), rect.y() + int(rect.h() / 2))
                img.draw_cross((local_x, local_y) ,color = (255, 0, 0))

                # 显示矩形信息（调试用）
                w, h = rect.w(), rect.h()
                area = w * h
                aspect_ratio = max(w, h) / min(w, h)
                print(f"矩形信息: 位置({rect.x()},{rect.y()}) 尺寸({w}x{h}) 面积({area}) 长宽比({aspect_ratio:.2f})")

                # --- PID控制逻辑 ---
                # 1. 计算与屏幕中心的误差
                error_x = local_x - (WIDTH / 2)
                error_y = local_y - (HEIGHT / 2) - rect_compensate

                # 2. X轴PID控制（步进电机）
                x_command_sent = False
                if abs(error_x) > X_DEAD_ZONE:
                    # 积分项累积
                    x_error_integral += error_x
                    # 积分限幅，防止积分饱和
                    if x_error_integral > X_MAX_INTEGRAL:
                        x_error_integral = X_MAX_INTEGRAL
                    elif x_error_integral < -X_MAX_INTEGRAL:
                        x_error_integral = -X_MAX_INTEGRAL

                    # 计算微分项（误差变化率）
                    x_error_derivative = error_x - x_error_last

                    # PID控制器计算输出
                    x_p_output = error_x * X_P_GAIN              # 比例项
                    x_i_output = x_error_integral * X_I_GAIN     # 积分项
                    x_d_output = x_error_derivative * X_D_GAIN   # 微分项
                    x_pid_output = x_p_output + x_i_output + x_d_output  # PID控制器总输出

                    # 转换为步数
                    steps_to_move = int(x_pid_output)

                    # 构建并发送X轴指令
                    steps_to_move = max(-9999, min(9999, steps_to_move))
                    sign = '+' if steps_to_move >= 0 else '-'
                    x_command = "x%c%04db" % (sign, abs(steps_to_move))

                    # 更新上一次误差
                    x_error_last = error_x
                    x_command_sent = True

                    print(f"X Error: {error_x:.1f}, Integral: {x_error_integral:.1f}, Derivative: {x_error_derivative:.1f}")
                    print(f"X P: {x_p_output:.1f}, I: {x_i_output:.1f}, D: {x_d_output:.1f}, Steps: {steps_to_move}")
                else:
                    # 在死区内时，不累积积分项，但更新上一次误差
                    x_error_last = error_x
                    print(f"X in dead zone, error: {error_x:.1f}")

                # 3. Y轴PID控制（串口舵机）
                y_command_sent = False
                if abs(error_y) > Y_DEAD_ZONE:
                    # 积分项累积
                    y_error_integral += error_y
                    # 积分限幅，防止积分饱和
                    if y_error_integral > Y_MAX_INTEGRAL:
                        y_error_integral = Y_MAX_INTEGRAL
                    elif y_error_integral < -Y_MAX_INTEGRAL:
                        y_error_integral = -Y_MAX_INTEGRAL

                    # 计算微分项（误差变化率）
                    y_error_derivative = error_y - y_error_last

                    # PID控制器计算输出
                    y_p_output = error_y * Y_P_GAIN              # 比例项
                    y_i_output = y_error_integral * Y_I_GAIN     # 积分项
                    y_d_output = y_error_derivative * Y_D_GAIN   # 微分项
                    y_pid_output = y_p_output + y_i_output + y_d_output  # PID控制器总输出

                    # 计算新的Y轴位置（绝对位置）
                    y_current_position += int(y_pid_output)

                    # 限制Y轴位置范围在1000-2000之间
                    y_current_position = max(1000, min(2000, y_current_position))

                    # 构建并发送Y轴指令
                    y_command = "y%04db" % y_current_position

                    # 更新上一次误差
                    y_error_last = error_y
                    y_command_sent = True

                    print(f"Y Error: {error_y:.1f}, Integral: {y_error_integral:.1f}, Derivative: {y_error_derivative:.1f}")
                    print(f"Y P: {y_p_output:.1f}, I: {y_i_output:.1f}, D: {y_d_output:.1f}, Position: {y_current_position}")
                else:
                    # 在死区内时，不累积积分项，但更新上一次误差
                    y_error_last = error_y
                    print(f"Y in dead zone, error: {error_y:.1f}")

                # 4. 发送控制指令
                if x_command_sent:
                    uart.send(x_command)
                if y_command_sent:
                    uart.send(y_command)

                # 5. 重置稳定计数（如果有任何轴发送了指令）
                if x_command_sent or y_command_sent:
                    stable_count = 0
                    is_tracking_stable = False
                    stable_command_sent = False

            else:
                # 未检测到矩形时，重置稳定性状态
                if stable_count > 0:
                    print("❌ 未检测到目标，重置稳定状态")
                stable_count = 0
                is_tracking_stable = False
                stable_command_sent = False
        else:
            rect = None
            print("识别功能关闭，等待开始指令0xAB")

        img = img.crop(x_scale = 320 / 160, y_scale = 240 / 120)
        img.draw_string_advanced(50, 50, 80, "fps: {}".format(fps.fps()), color = (255, 0, 0))#打印帧率
        Display.show_image(img)#显示在屏幕上
        gc.collect()#执行垃圾回收

except BaseException as e:# 捕获所有其他异常
    import sys
    sys.print_exception(e)
    sensor.stop()
    # 反初始化显示模块，串口模块
    Display.deinit()
    # 设置退出点，允许进入睡眠模式
    time.sleep_ms(100)
    # 释放媒体缓冲区
    MediaManager.deinit()



